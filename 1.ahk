!K::
{
    ; 启动WindowsTerminal
    ProcessID := Run("C:\tools\WindowsTerminal\WindowsTerminal.exe cmd /k `"" . A_Desktop . "\kimiReverse.exe`"")
    
    ; 尝试多种窗口标识符等待窗口出现
    WindowFound := False
    Loop 50 {  ; 最多等待5秒
        ; 尝试不同的窗口标识符
        if WinExist("ahk_pid " . ProcessID) {
            WindowID := "ahk_pid " . ProcessID
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe WindowsTerminal.exe") {
            WindowID := "ahk_exe WindowsTerminal.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            WindowID := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
            WindowFound := True
            break
        }
        else if WinExist("Windows PowerShell") {
            WindowID := "Windows PowerShell"
            WindowFound := True
            break
        }
        Sleep 100
    }
    
    if (!WindowFound) {
        MsgBox("WindowsTerminal 窗口未找到，请检查路径和进程")
        return
    }
    
    ; 等待窗口完全加载
    Sleep 500
    
    ; 再次确认窗口存在
    if (!WinExist(WindowID)) {
        MsgBox("窗口在操作前消失了")
        return
    }
    
    ; 检查是否有第二屏幕
    MonitorCount := MonitorGetCount()
    if (MonitorCount < 2) {
        MsgBox("只检测到一个屏幕，将在主屏幕最大化")
        try {
            WinMaximize WindowID
            WinActivate WindowID
        } catch Error as e {
            MsgBox("最大化失败: " . e.Message)
        }
        return
    }
    
    ; 获取第二屏幕的位置和尺寸
    MonitorGet 2, &Left, &Top, &Right, &Bottom
    
    ; 分步骤执行窗口操作，并添加错误处理
    try {
        ; 先激活窗口
        WinActivate WindowID
        Sleep 200
        
        ; 移动窗口到第二屏幕
        WinMove Left, Top, Right-Left, Bottom-Top, WindowID
        Sleep 200
        
        ; 最大化窗口
        WinMaximize WindowID
        Sleep 200
        
        ; 再次激活确保获得焦点
        WinActivate WindowID
        
    } catch Error as e {
        MsgBox("窗口操作失败: " . e.Message . "`n窗口ID: " . WindowID)
        
        ; 尝试备用方案：直接最大化在当前位置
        try {
            WinMaximize WindowID
            WinActivate WindowID
        } catch {
            MsgBox("备用方案也失败了")
        }
    }
}