{"time":"2025-08-04T17:37:37.532352+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":99},"msg":"Using cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
{"time":"2025-08-04T17:37:37.547278+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.loadProviders.func1","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":103},"msg":"Updating provider cache in background"}
{"time":"2025-08-04T17:37:38.7683363+08:00","level":"INFO","msg":"OK   20250424200609_initial.sql (2.06ms)"}
{"time":"2025-08-04T17:37:38.7693551+08:00","level":"INFO","msg":"OK   20250515105448_add_summary_message_id.sql (1.02ms)"}
{"time":"2025-08-04T17:37:38.7698637+08:00","level":"INFO","msg":"OK   20250624000000_add_created_at_indexes.sql (508.6µs)"}
{"time":"2025-08-04T17:37:38.7708883+08:00","level":"INFO","msg":"OK   20250627000000_add_provider_to_messages.sql (1.02ms)"}
{"time":"2025-08-04T17:37:38.7708883+08:00","level":"INFO","msg":"goose: successfully migrated database to version: 20250627000000"}
{"time":"2025-08-04T17:37:38.7708883+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/app.(*App).initLSPClients","file":"/home/<USER>/work/crush/crush/internal/app/lsp.go","line":18},"msg":"LSP clients initialization started in background"}
{"time":"2025-08-04T17:37:38.7735523+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"task"}
{"time":"2025-08-04T17:37:38.7735523+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"task"}
{"time":"2025-08-04T17:37:38.7757012+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":172},"msg":"Initializing agent tools","agent":"coder"}
{"time":"2025-08-04T17:37:38.7757012+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/llm/agent.NewAgent.func1.1","file":"/home/<USER>/work/crush/crush/internal/llm/agent/agent.go","line":174},"msg":"Initialized agent tools","agent":"coder"}
{"time":"2025-08-04T17:37:43.3127663+08:00","level":"INFO","source":{"function":"github.com/charmbracelet/crush/internal/config.saveProvidersInCache","file":"/home/<USER>/work/crush/crush/internal/config/provider.go","line":48},"msg":"Saving cached provider data","path":"C:\\Users\\<USER>\\AppData\\Local\\crush\\providers.json"}
