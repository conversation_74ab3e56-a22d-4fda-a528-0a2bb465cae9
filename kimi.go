package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

var (
	user32                     = windows.NewLazySystemDLL("user32.dll")
	procFindWindowW            = user32.NewProc("FindWindowW")
	procGetWindowTextW         = user32.NewProc("GetWindowTextW")
	procShowWindow             = user32.NewProc("ShowWindow")
	procSetForegroundWindow    = user32.NewProc("SetForegroundWindow")
	procEnumWindows            = user32.NewProc("EnumWindows")
	procGetWindowThreadProcessId = user32.NewProc("GetWindowThreadProcessId")
	procGetSystemMetrics       = user32.NewProc("GetSystemMetrics")
	procMoveWindow             = user32.NewProc("MoveWindow")
	procEnumDisplayMonitors    = user32.NewProc("EnumDisplayMonitors")
	procGetMonitorInfoW        = user32.NewProc("GetMonitorInfoW")
)

const (
	SW_MAXIMIZE = 3
	SM_CMONITORS = 80
)

type RECT struct {
	Left, Top, Right, Bottom int32
}

type MONITORINFO struct {
	CbSize    uint32
	RcMonitor RECT
	RcWork    RECT
	DwFlags   uint32
}

type WindowInfo struct {
	Handle uintptr
	Title  string
	PID    uint32
}

// 枚举窗口的回调函数
func enumWindowsProc(hwnd uintptr, lParam uintptr) uintptr {
	windows := (*[]WindowInfo)(unsafe.Pointer(lParam))
	
	// 获取窗口标题
	titleBuf := make([]uint16, 256)
	ret, _, _ := procGetWindowTextW.Call(hwnd, uintptr(unsafe.Pointer(&titleBuf[0])), 256)
	if ret == 0 {
		return 1
	}
	
	title := windows.UTF16ToString(titleBuf)
	if title == "" {
		return 1
	}
	
	// 获取进程ID
	var pid uint32
	procGetWindowThreadProcessId.Call(hwnd, uintptr(unsafe.Pointer(&pid)))
	
	*windows = append(*windows, WindowInfo{
		Handle: hwnd,
		Title:  title,
		PID:    pid,
	})
	
	return 1
}

// 获取所有窗口
func getAllWindows() []WindowInfo {
	var windows []WindowInfo
	procEnumWindows.Call(
		syscall.NewCallback(enumWindowsProc),
		uintptr(unsafe.Pointer(&windows)),
	)
	return windows
}

// 查找WindowsTerminal窗口
func findWindowsTerminal(targetPID uint32) *WindowInfo {
	windows := getAllWindows()
	
	for _, win := range windows {
		// 首先尝试通过PID匹配
		if targetPID != 0 && win.PID == targetPID {
			return &win
		}
		
		// 然后尝试通过窗口标题匹配
		title := win.Title
		if title == "Windows PowerShell" ||
		   title == "Windows Terminal" ||
		   title == "命令提示符" ||
		   title == "Administrator: Windows PowerShell" {
			return &win
		}
	}
	
	return nil
}

// 获取显示器数量
func getMonitorCount() int {
	ret, _, _ := procGetSystemMetrics.Call(SM_CMONITORS)
	return int(ret)
}

// 最大化窗口
func maximizeWindow(hwnd uintptr) error {
	ret, _, err := procShowWindow.Call(hwnd, SW_MAXIMIZE)
	if ret == 0 {
		return fmt.Errorf("failed to maximize window: %v", err)
	}
	
	// 激活窗口
	procSetForegroundWindow.Call(hwnd)
	return nil
}

// 移动窗口到指定位置和大小
func moveWindow(hwnd uintptr, x, y, width, height int) error {
	ret, _, err := procMoveWindow.Call(hwnd, uintptr(x), uintptr(y), uintptr(width), uintptr(height), 1)
	if ret == 0 {
		return fmt.Errorf("failed to move window: %v", err)
	}
	return nil
}

// 启动kimiReverse
func runKimiReverse() error {
	// 获取桌面路径
	homeDir, err := os.UserHomeDir()
	if err != nil {
		return fmt.Errorf("failed to get home directory: %v", err)
	}
	
	desktopPath := filepath.Join(homeDir, "Desktop")
	kimiPath := filepath.Join(desktopPath, "kimiReverse.exe")
	
	fmt.Printf("Running kimiReverse from: %s\n", kimiPath)
	
	// 构建WindowsTerminal命令
	terminalPath := `C:\tools\WindowsTerminal\WindowsTerminal.exe`
	cmdArgs := fmt.Sprintf(`cmd /k "%s"`, kimiPath)
	
	// 启动WindowsTerminal
	cmd := exec.Command(terminalPath, cmdArgs)
	
	err = cmd.Start()
	if err != nil {
		return fmt.Errorf("failed to start WindowsTerminal: %v", err)
	}
	
	// 获取进程PID
	pid := uint32(cmd.Process.Pid)
	fmt.Printf("WindowsTerminal started with PID: %d\n", pid)
	
	// 等待窗口出现
	var window *WindowInfo
	for i := 0; i < 50; i++ {
		time.Sleep(100 * time.Millisecond)
		
		window = findWindowsTerminal(pid)
		if window != nil {
			fmt.Printf("Found window: %s (Handle: %x)\n", window.Title, window.Handle)
			break
		}
	}
	
	if window == nil {
		return fmt.Errorf("WindowsTerminal window not found after 5 seconds")
	}
	
	// 等待窗口完全加载
	time.Sleep(500 * time.Millisecond)
	
	// 检查是否有第二屏幕
	monitorCount := getMonitorCount()
	if monitorCount < 2 {
		fmt.Println("Only one monitor detected, maximizing on primary screen")
		err = maximizeWindow(window.Handle)
		if err != nil {
			return fmt.Errorf("failed to maximize window: %v", err)
		}
		return nil
	}
	
	fmt.Printf("Detected %d monitors, moving to second screen\n", monitorCount)
	
	// 简化的第二屏幕处理：假设第二屏幕在右侧
	// 这里可以根据实际情况调整屏幕尺寸
	secondScreenX := 1920  // 假设主屏幕宽度为1920
	secondScreenY := 0
	secondScreenWidth := 1920
	secondScreenHeight := 1080
	
	// 激活窗口
	procSetForegroundWindow.Call(window.Handle)
	time.Sleep(200 * time.Millisecond)
	
	// 移动到第二屏幕
	err = moveWindow(window.Handle, secondScreenX, secondScreenY, secondScreenWidth, secondScreenHeight)
	if err != nil {
		fmt.Printf("Failed to move window: %v, trying to maximize on current screen\n", err)
		return maximizeWindow(window.Handle)
	}
	
	time.Sleep(200 * time.Millisecond)
	
	// 最大化窗口
	err = maximizeWindow(window.Handle)
	if err != nil {
		return fmt.Errorf("failed to maximize window: %v", err)
	}
	
	// 再次激活确保获得焦点
	procSetForegroundWindow.Call(window.Handle)
	
	fmt.Println("WindowsTerminal moved to second screen and maximized successfully!")
	return nil
}

func main() {
	fmt.Println("Starting kimiReverse...")
	
	err := runKimiReverse()
	if err != nil {
		log.Fatalf("Error: %v", err)
	}
	
	fmt.Println("Done!")
}
