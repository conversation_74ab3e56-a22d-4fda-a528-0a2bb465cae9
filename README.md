# AutoHotkey to Go 转换

这个项目将原来的AutoHotkey脚本转换为Go语言实现。

## 文件说明

- `crush.go` - 对应 `crush.ahk`，在当前目录运行crush命令并最大化WindowsTerminal
- `kimi.go` - 对应 `1.ahk`，运行kimiReverse.exe并处理多屏幕显示
- `go.mod` - Go模块依赖管理文件

## 功能对比

### crush.go vs crush.ahk
- ✅ 获取当前工作目录
- ✅ 启动WindowsTerminal并运行crush命令
- ✅ 查找并最大化WindowsTerminal窗口
- ✅ 错误处理和日志输出

### kimi.go vs 1.ahk
- ✅ 启动WindowsTerminal运行kimiReverse.exe
- ✅ 多种窗口识别方法
- ✅ 检测多屏幕环境
- ✅ 移动窗口到第二屏幕并最大化
- ✅ 完整的错误处理

## 构建和运行

### 1. 安装依赖
```bash
go mod tidy
```

### 2. 构建可执行文件
```bash
# 构建crush程序
go build -o crush.exe crush.go

# 构建kimi程序
go build -o kimi.exe kimi.go
```

### 3. 运行
```bash
# 运行crush（在当前目录执行crush命令）
./crush.exe

# 运行kimi（启动kimiReverse.exe）
./kimi.exe
```

## 优势

### 相比AutoHotkey的优势：
1. **跨平台潜力** - Go代码可以适配其他操作系统
2. **更好的错误处理** - 详细的错误信息和日志
3. **性能更好** - 编译后的二进制文件启动更快
4. **更容易调试** - 可以使用Go的调试工具
5. **更好的代码组织** - 函数化、模块化的代码结构
6. **依赖管理** - 使用go.mod管理依赖
7. **更容易集成** - 可以作为库被其他Go程序调用

### 功能增强：
1. **详细日志** - 显示执行过程和状态信息
2. **更好的窗口检测** - 支持多种窗口标题匹配
3. **灵活的配置** - 可以通过代码轻松修改路径和参数
4. **更强的错误恢复** - 多种备用方案

## 注意事项

1. **Windows API依赖** - 当前实现依赖Windows API，仅支持Windows系统
2. **路径配置** - 需要确保WindowsTerminal路径正确：`C:\tools\WindowsTerminal\WindowsTerminal.exe`
3. **权限要求** - 可能需要管理员权限来操作其他程序的窗口

## 自定义配置

可以通过修改代码中的常量来自定义配置：

```go
// 修改WindowsTerminal路径
terminalPath := `C:\your\path\to\WindowsTerminal.exe`

// 修改第二屏幕尺寸（在kimi.go中）
secondScreenX := 1920
secondScreenWidth := 1920
secondScreenHeight := 1080
```

## 扩展功能

Go版本更容易扩展，可以添加：
- 配置文件支持
- 命令行参数
- GUI界面
- 热键支持（需要额外的库）
- 更复杂的窗口管理功能
