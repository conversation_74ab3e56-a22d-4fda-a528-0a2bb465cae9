package main

import (
	"fmt"
	"log"
	"os"
	"os/exec"
	"syscall"
	"time"
	"unsafe"

	"golang.org/x/sys/windows"
)

var (
	user32                       = windows.NewLazySystemDLL("user32.dll")
	procFindWindowW              = user32.NewProc("FindWindowW")
	procGetWindowTextW           = user32.NewProc("GetWindowTextW")
	procShowWindow               = user32.NewProc("ShowWindow")
	procSetForegroundWindow      = user32.NewProc("SetForegroundWindow")
	procEnumWindows              = user32.NewProc("EnumWindows")
	procGetWindowThreadProcessId = user32.NewProc("GetWindowThreadProcessId")
	procGetForegroundWindow      = user32.NewProc("GetForegroundWindow")
	procGetClassNameW            = user32.NewProc("GetClassNameW")
)

const (
	SW_MAXIMIZE = 3
)

type WindowInfo struct {
	Handle uintptr
	Title  string
	PID    uint32
}

// 枚举窗口的回调函数
func enumWindowsProc(hwnd uintptr, lParam uintptr) uintptr {
	windows := (*[]WindowInfo)(unsafe.Pointer(lParam))

	// 获取窗口标题
	titleBuf := make([]uint16, 256)
	ret, _, _ := procGetWindowTextW.Call(hwnd, uintptr(unsafe.Pointer(&titleBuf[0])), 256)
	if ret == 0 {
		return 1 // 继续枚举
	}

	title := syscall.UTF16ToString(titleBuf)
	if title == "" {
		return 1 // 继续枚举
	}

	// 获取进程ID
	var pid uint32
	procGetWindowThreadProcessId.Call(hwnd, uintptr(unsafe.Pointer(&pid)))

	*windows = append(*windows, WindowInfo{
		Handle: hwnd,
		Title:  title,
		PID:    pid,
	})

	return 1 // 继续枚举
}

// 获取所有窗口
func getAllWindows() []WindowInfo {
	var windows []WindowInfo
	procEnumWindows.Call(
		syscall.NewCallback(enumWindowsProc),
		uintptr(unsafe.Pointer(&windows)),
	)
	return windows
}

// 查找WindowsTerminal窗口
func findWindowsTerminal(targetPID uint32) *WindowInfo {
	windows := getAllWindows()

	for _, win := range windows {
		// 首先尝试通过PID匹配
		if targetPID != 0 && win.PID == targetPID {
			return &win
		}

		// 然后尝试通过窗口标题匹配
		title := win.Title
		if title == "Windows PowerShell" ||
			title == "Windows Terminal" ||
			title == "命令提示符" ||
			title == "Administrator: Windows PowerShell" {
			return &win
		}
	}

	return nil
}

// 最大化窗口
func maximizeWindow(hwnd uintptr) error {
	ret, _, err := procShowWindow.Call(hwnd, SW_MAXIMIZE)
	if ret == 0 {
		return fmt.Errorf("failed to maximize window: %v", err)
	}

	// 激活窗口
	procSetForegroundWindow.Call(hwnd)
	return nil
}

// 获取当前活动的资源管理器路径
func getCurrentExplorerPath() (string, error) {
	// 检查当前活动窗口是否是资源管理器
	activeWindow, _, _ := procGetForegroundWindow.Call()
	if activeWindow == 0 {
		return "", fmt.Errorf("no active window found")
	}

	// 获取窗口类名
	className := make([]uint16, 256)
	procGetClassNameW.Call(activeWindow, uintptr(unsafe.Pointer(&className[0])), 256)
	classNameStr := syscall.UTF16ToString(className)

	if classNameStr == "CabinetWClass" || classNameStr == "ExploreWClass" {
		// 这是资源管理器窗口，尝试获取路径
		return getExplorerPathViaCOM(activeWindow)
	}

	return "", fmt.Errorf("active window is not explorer")
}

// 通过COM接口获取资源管理器路径
func getExplorerPathViaCOM(hwnd uintptr) (string, error) {
	// 初始化COM
	ole32 := syscall.NewLazyDLL("ole32.dll")
	procCoInitialize := ole32.NewProc("CoInitialize")
	procCoUninitialize := ole32.NewProc("CoUninitialize")

	procCoInitialize.Call(0)
	defer procCoUninitialize.Call()

	// 这里简化处理，直接返回工作目录作为备用
	// 在实际应用中，可以使用更复杂的COM调用来获取确切路径
	currentDir, _ := os.Getwd()
	return currentDir, nil
}

// 启动crush命令
func runCrush() error {
	// 尝试获取当前资源管理器路径
	currentDir, err := getCurrentExplorerPath()
	if err != nil {
		// 如果获取失败，使用当前工作目录
		currentDir, err = os.Getwd()
		if err != nil {
			return fmt.Errorf("failed to get current directory: %v", err)
		}
		fmt.Printf("Using working directory (explorer path not available): %s\n", currentDir)
	} else {
		fmt.Printf("Using explorer directory: %s\n", currentDir)
	}

	fmt.Printf("Running crush in directory: %s\n", currentDir)

	// 构建WindowsTerminal命令
	terminalPath := `C:\tools\WindowsTerminal\WindowsTerminal.exe`
	args := []string{
		"--startingDirectory", currentDir,
		"cmd", "/k", "crush",
	}

	// 启动WindowsTerminal
	cmd := exec.Command(terminalPath, args...)
	cmd.Dir = currentDir

	err = cmd.Start()
	if err != nil {
		return fmt.Errorf("failed to start WindowsTerminal: %v", err)
	}

	// 获取进程PID
	pid := uint32(cmd.Process.Pid)
	fmt.Printf("WindowsTerminal started with PID: %d\n", pid)

	// 等待窗口出现并最大化
	for i := 0; i < 50; i++ {
		time.Sleep(100 * time.Millisecond)

		window := findWindowsTerminal(pid)
		if window != nil {
			fmt.Printf("Found window: %s (Handle: %x)\n", window.Title, window.Handle)

			// 等待窗口完全加载
			time.Sleep(500 * time.Millisecond)

			// 最大化窗口
			err = maximizeWindow(window.Handle)
			if err != nil {
				return fmt.Errorf("failed to maximize window: %v", err)
			}

			fmt.Println("WindowsTerminal maximized successfully!")
			return nil
		}
	}

	return fmt.Errorf("WindowsTerminal window not found after 5 seconds")
}

func main() {
	fmt.Println("Starting crush command...")

	err := runCrush()
	if err != nil {
		log.Fatalf("Error: %v", err)
	}

	fmt.Println("Done!")
}
