!C::
{
    ; 获取当前活动窗口的工作目录
    CurrentDir := ""

    ; 检查当前活动窗口类型并获取相应的工作目录
    if WinActive("ahk_class CabinetWClass") or WinActive("ahk_class ExploreWClass") {
        ; 获取资源管理器当前路径
        try {
            shell := ComObject("Shell.Application")
            windows := shell.Windows
            activeHwnd := WinGetID("A")

            for window in windows {
                try {
                    if window.HWND = activeHwnd {
                        CurrentDir := window.Document.Folder.Self.Path
                        break
                    }
                }
            }
        }
    }
    else if WinActive("ahk_exe Code.exe") {
        ; 获取VSCode当前工作目录
        try {
            ; 方法1: 从VSCode窗口标题获取路径信息
            windowTitle := WinGetTitle("A")

            ; VSCode窗口标题通常包含完整路径，格式如: "文件名 - 工作目录路径 - Visual Studio Code"
            ; 或者直接显示路径
            if InStr(windowTitle, "Visual Studio Code") {
                ; 移除 "Visual Studio Code" 部分
                titlePart := StrReplace(windowTitle, " - Visual Studio Code", "")

                ; 如果标题中包含路径分隔符，可能是完整路径
                if InStr(titlePart, "\") {
                    ; 尝试提取路径部分
                    if RegExMatch(titlePart, ".*\\(.+)$", &match) {
                        ; 提取最后的目录名
                        workspaceName := match[1]
                    } else {
                        workspaceName := titlePart
                    }
                } else {
                    workspaceName := titlePart
                }

                ; 方法2: 尝试从当前用户的常见VSCode工作区位置查找
                possiblePaths := [
                    "C:\Users\<USER>\Desktop\test\" . workspaceName,
                    "C:\Users\<USER>\Desktop\" . workspaceName,
                    "C:\Users\<USER>\Documents\" . workspaceName,
                    "C:\Users\<USER>\source\repos\" . workspaceName,
                    "D:\projects\" . workspaceName,
                    "D:\" . workspaceName,
                    "C:\projects\" . workspaceName
                ]

                for path in possiblePaths {
                    if DirExist(path) {
                        CurrentDir := path
                        break
                    }
                }
            }
        }
    }

    ; 如果没有获取到目录，使用默认方法
    if (CurrentDir = "") {
        CurrentDir := A_WorkingDir
    }

    ; 启动WindowsTerminal并在指定目录运行crush命令
    ; 使用--startingDirectory参数直接在指定目录启动
    ProcessID := Run("C:\tools\WindowsTerminal\WindowsTerminal.exe --startingDirectory `"" . CurrentDir . "`" cmd /k crush")
    
    ; 尝试多种窗口标识符等待窗口出现
    WindowFound := False
    Loop 50 {  ; 最多等待5秒
        ; 尝试不同的窗口标识符
        if WinExist("ahk_pid " . ProcessID) {
            WindowID := "ahk_pid " . ProcessID
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe WindowsTerminal.exe") {
            WindowID := "ahk_exe WindowsTerminal.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            WindowID := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
            WindowFound := True
            break
        }
        else if WinExist("Windows PowerShell") {
            WindowID := "Windows PowerShell"
            WindowFound := True
            break
        }
        Sleep 100
    }
    
    if (!WindowFound) {
        MsgBox("WindowsTerminal 窗口未找到，请检查路径和进程")
        return
    }

    ; 等待窗口完全加载
    Sleep 500

    ; 再次确认窗口存在
    if (!WinExist(WindowID)) {
        MsgBox("窗口在操作前消失了")
        return
    }

    ; 简单的窗口最大化操作
    try {
        WinMaximize WindowID
        WinActivate WindowID
    } catch Error as e {
        MsgBox("最大化失败: " . e.Message)
    }
}
