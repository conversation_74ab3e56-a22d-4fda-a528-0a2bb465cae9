!C::
{
    ; 获取当前脚本所在目录
    CurrentDir := A_ScriptDir

    ; 启动WindowsTerminal并在当前目录运行crush命令
    ; 参考1.ahk的方式，使用完整路径启动WindowsTerminal
    ProcessID := Run("C:\tools\WindowsTerminal\WindowsTerminal.exe cmd /k `"cd /d `"" . CurrentDir . "`" && crush`"")
    
    ; 尝试多种窗口标识符等待窗口出现
    WindowFound := False
    Loop 50 {  ; 最多等待5秒
        ; 尝试不同的窗口标识符
        if WinExist("ahk_pid " . ProcessID) {
            WindowID := "ahk_pid " . ProcessID
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe WindowsTerminal.exe") {
            WindowID := "ahk_exe WindowsTerminal.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            WindowID := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
            WindowFound := True
            break
        }
        else if WinExist("Windows PowerShell") {
            WindowID := "Windows PowerShell"
            WindowFound := True
            break
        }
        Sleep 100
    }
    
    if (!WindowFound) {
        MsgBox("WindowsTerminal 窗口未找到，请检查路径和进程")
        return
    }

    ; 等待窗口完全加载
    Sleep 500

    ; 再次确认窗口存在
    if (!WinExist(WindowID)) {
        MsgBox("窗口在操作前消失了")
        return
    }

    ; 简单的窗口最大化操作
    try {
        WinMaximize WindowID
        WinActivate WindowID
    } catch Error as e {
        MsgBox("最大化失败: " . e.Message)
    }
}
