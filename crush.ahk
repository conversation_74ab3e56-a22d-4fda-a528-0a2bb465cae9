!C::
{
    ; 获取当前脚本所在目录
    CurrentDir := A_ScriptDir
    
    ; 启动WindowsTerminal并在当前目录运行crush命令
    ; 尝试多种方式启动Windows Terminal
    ProcessID := 0

    ; 方法1: 尝试使用wt.exe
    try {
        ProcessID := Run("wt.exe cmd /k `"cd /d `"" . CurrentDir . "`" && crush`"")
    } catch {
        ; 方法2: 尝试使用完整路径的Windows Terminal
        try {
            ProcessID := Run("C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps\wt.exe cmd /k `"cd /d `"" . CurrentDir . "`" && crush`"")
        } catch {
            ; 方法3: 使用系统默认的cmd窗口
            try {
                ProcessID := Run("cmd /k `"cd /d `"" . CurrentDir . "`" && crush`"")
            } catch {
                MsgBox("无法启动终端，请检查系统配置")
                return
            }
        }
    }
    
    ; 尝试多种窗口标识符等待窗口出现
    WindowFound := False
    Loop 50 {  ; 最多等待5秒
        ; 尝试不同的窗口标识符
        if WinExist("ahk_pid " . ProcessID) {
            WindowID := "ahk_pid " . ProcessID
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe WindowsTerminal.exe") {
            WindowID := "ahk_exe WindowsTerminal.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class CASCADIA_HOSTING_WINDOW_CLASS") {
            WindowID := "ahk_class CASCADIA_HOSTING_WINDOW_CLASS"
            WindowFound := True
            break
        }
        else if WinExist("ahk_exe cmd.exe") {
            WindowID := "ahk_exe cmd.exe"
            WindowFound := True
            break
        }
        else if WinExist("ahk_class ConsoleWindowClass") {
            WindowID := "ahk_class ConsoleWindowClass"
            WindowFound := True
            break
        }
        else if WinExist("Windows Terminal") {
            WindowID := "Windows Terminal"
            WindowFound := True
            break
        }
        Sleep 100
    }
    
    if (!WindowFound) {
        MsgBox("WindowsTerminal 窗口未找到，请检查Windows Terminal是否已安装")
        return
    }
    
    ; 等待窗口完全加载
    Sleep 500
    
    ; 再次确认窗口存在
    if (!WinExist(WindowID)) {
        MsgBox("窗口在操作前消失了")
        return
    }
    
    ; 执行窗口最大化操作
    try {
        ; 先激活窗口
        WinActivate WindowID
        Sleep 200
        
        ; 最大化窗口
        WinMaximize WindowID
        Sleep 200
        
        ; 再次激活确保获得焦点
        WinActivate WindowID
        
        ; 显示成功消息（可选，可以注释掉）
        ; MsgBox("crush命令已在当前目录启动，WindowsTerminal已最大化")
        
    } catch Error as e {
        MsgBox("窗口操作失败: " . e.Message . "`n窗口ID: " . WindowID)
        
        ; 尝试备用方案：直接最大化在当前位置
        try {
            WinMaximize WindowID
            WinActivate WindowID
        } catch {
            MsgBox("备用方案也失败了")
        }
    }
}
